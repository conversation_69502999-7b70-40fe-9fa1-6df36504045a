import React from "react";

import Link from "next/link";
import styled from "styled-components";
import { Row } from "antd";

import { SvgIconPatientList } from "@/components/ui/Icon/IconPatientList";

// Main container for the start page sidebar
const SidebarContainer = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 40px;
  padding: 20px;
  background-color: #005bac;
  border-top-right-radius: 10px;
`;

// Base button styles
const BaseNavigationButton = styled(Link)`
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.2s ease;
`;

// Primary large navigation button (for main features)
const PrimaryNavigationButton = styled(BaseNavigationButton)`
  width: 100%;
  height: 60px;
  background-color: white;
  border-radius: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
    color: #333;
  }
`;

// Secondary small navigation button (for grouped features)
const SecondaryNavigationButton = styled(BaseNavigationButton)`
  flex: 1;
  height: 48px;
  background-color: #fff;
  border-radius: 8px;
  font-size: 14px;
  color: #243544;
  letter-spacing: -1px;

  &:hover {
    background-color: #f5f5f5;
    color: #243544;
  }
`;

// Blue action button (for management features)
const ActionButton = styled(BaseNavigationButton)`
  flex: 1;
  height: 40px;
  font-size: 14px;
  background-color: #329ce7;
  border-radius: 8px;
  color: #fff;

  &:hover {
    background-color: #2a8bc7;
    color: #fff;
  }
`;

// Special patient list button with icon
const PatientListNavigationButton = styled(BaseNavigationButton)`
  width: 208px;
  height: 40px;
  background-color: #329ce7;
  border-radius: 8px;
  color: white;

  &:hover {
    background-color: #2a8bc7;
    color: white;
  }

  .icon-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    background-color: #ccc;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  .label {
    flex: 1;
    text-align: center;
    font-size: 14px;
  }
`;

// New registration button with special styling
const NewRegistrationButton = styled(BaseNavigationButton)`
  width: 120px;
  height: 36px;
  border-radius: 24px;
  background-color: #43c3d5;
  color: #fff;
  font-size: 14px;

  &:hover {
    background-color: #3ab0c1;
    color: #fff;
  }
`;

// Layout containers for button groups
const ButtonGroupRow = styled.div`
  width: 100%;
  display: flex;
  gap: 20px;
`;

const PatientManagementRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

// Main component with improved naming and structure
export const StartPageSidebar = () => {
  return (
    <SidebarContainer>
      {/* Primary navigation section */}
      <Row gutter={[0, 20]}>
        {/* Main clinic map feature */}
        <PrimaryNavigationButton href="/clinic-map">
          GMO クリニック・マップ
        </PrimaryNavigationButton>

        {/* Digital health services group */}
        <ButtonGroupRow>
          <SecondaryNavigationButton href="/online-consultation">
            オンライン診療
          </SecondaryNavigationButton>
          <SecondaryNavigationButton href="/web-inquiry">
            WEB問診
          </SecondaryNavigationButton>
          <SecondaryNavigationButton href="/ai-assist">
            AIアシスト
          </SecondaryNavigationButton>
        </ButtonGroupRow>

        {/* AI Chart feature */}
        <PrimaryNavigationButton href="/ai-chart">
          AIチャート
        </PrimaryNavigationButton>
      </Row>

      {/* Management and administrative section */}
      <Row gutter={[0, 20]}>
        {/* Patient management */}
        <PatientManagementRow>
          <PatientListNavigationButton href="/patients">
            <div className="icon-container">
              <SvgIconPatientList />
            </div>
            <span className="label">患者リスト</span>
          </PatientListNavigationButton>
          <NewRegistrationButton href="/patients/new">
            新規登録
          </NewRegistrationButton>
        </PatientManagementRow>

        {/* Reception and reservation management */}
        <ButtonGroupRow>
          <ActionButton href="/reception">受付一覧</ActionButton>
          <ActionButton href="/reservations">予約管理</ActionButton>
        </ButtonGroupRow>

        {/* Laboratory, receipt, and report management */}
        <ButtonGroupRow>
          <ActionButton href="/lab-integration">検査連携</ActionButton>
          <ActionButton href="/receipts">レセプト</ActionButton>
          <ActionButton href="/reports">帳票印刷</ActionButton>
        </ButtonGroupRow>
      </Row>
    </SidebarContainer>
  );
};

// Export with original name for backward compatibility
export const LeftContent = StartPageSidebar;
