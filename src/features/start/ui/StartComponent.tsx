import styled from "styled-components";

const StartWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
`;

const Title = styled.h1`
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
`;

export const StartComponent: React.FC = () => {
  return (
    <StartWrapper>
      <Title>ホームページ</Title>
      <Description>AIチャートへようこそ</Description>
    </StartWrapper>
  );
};
