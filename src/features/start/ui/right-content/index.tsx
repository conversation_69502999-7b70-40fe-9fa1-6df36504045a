import React from "react";

import styled from "styled-components";

import { ReserveSummarySection } from "./ReserveSummarySection";
import { HospitalBoard } from "./HospitalBoard";
import { NotifySection } from "./NotifySection";
import { ScheduleSection } from "./ScheduleSection";
import { TaskSection } from "./TaskSection";

const RightContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const LeftContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 50%;
`;

const RightContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 50%;
`;

const ContentWrapper = styled.div`
  display: flex;
  gap: 20px;
`;

export const RightContent = () => {
  return (
    <RightContentContainer>
      <ContentWrapper>
        <LeftContainer>
          <ReserveSummarySection />
          <ScheduleSection />
        </LeftContainer>
        <RightContainer>
          <HospitalBoard />
          <NotifySection />
        </RightContainer>
      </ContentWrapper>
      <TaskSection />
    </RightContentContainer>
  );
};
