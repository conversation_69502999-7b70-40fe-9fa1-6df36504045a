import React from "react";

import styled from "styled-components";
import dayjs from "dayjs";
import "dayjs/locale/ja";

// Set Japanese locale for dayjs
dayjs.locale("ja");

const ReserveSummarySectionContainer = styled.div`
  height: 160px;
  border: 1px solid #e2e3e5;
  border-radius: 6px;
  background-color: #fff;
  display: flex;
  overflow: hidden;
`;

const DateSection = styled.div`
  background-color: #005bac;
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  min-width: 200px;
`;

const YearText = styled.div`
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
`;

const DateText = styled.div`
  font-size: 36px;
  font-weight: bold;
  line-height: 1.1;
  margin-bottom: 8px;
`;

const DayText = styled.div`
  font-size: 16px;
`;

const Badge = styled.div`
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: #8b6914;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
`;

const StatsSection = styled.div`
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const StatsGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StatsTitle = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #243544;
`;

const StatsRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
`;

const StatsLabel = styled.div`
  color: #243544;
`;

const StatsValue = styled.div`
  color: #4ebbe0;
  font-weight: bold;
`;

export const ReserveSummarySection = () => {
  const currentDate = dayjs();
  const year = currentDate.format("YYYY年");
  const monthDay = currentDate.format("M月D日");
  const dayOfWeek = currentDate.format("dddd");

  return (
    <ReserveSummarySectionContainer>
      <DateSection>
        <YearText>{year}</YearText>
        <DateText>{monthDay}</DateText>
        <DayText>{dayOfWeek}</DayText>
      </DateSection>
      <StatsSection>
        <StatsGroup>
          <StatsRow>
            <StatsLabel>予約</StatsLabel>
            <StatsValue>全99件</StatsValue>
          </StatsRow>
          <StatsRow>
            <StatsLabel>対面</StatsLabel>
            <StatsValue>99件</StatsValue>
          </StatsRow>
          <StatsRow>
            <StatsLabel>オンライン</StatsLabel>
            <StatsValue>99件</StatsValue>
          </StatsRow>
        </StatsGroup>
        <StatsGroup>
          <StatsRow>
            <StatsTitle>未完了タスク</StatsTitle>
            <StatsValue>全99件</StatsValue>
          </StatsRow>
        </StatsGroup>
      </StatsSection>
    </ReserveSummarySectionContainer>
  );
};
